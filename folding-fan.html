<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>折扇图形</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }

        h1 {
            color: white;
            margin-bottom: 40px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .fan-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 50px;
        }

        /* 方法1: 使用多个扇叶组成折扇 */
        .folding-fan {
            position: relative;
            width: 300px;
            height: 200px;
            margin: 50px auto;
        }

        .fan-blade {
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 40px;
            height: 180px;
            background-image: url('https://picsum.photos/200/400?random=1');
            background-size: cover;
            background-position: center;
            transform-origin: bottom center;
            border-radius: 20px 20px 0 0;
            border: 2px solid rgba(139, 69, 19, 0.8);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        /* 每个扇叶的旋转角度 */
        .fan-blade:nth-child(1) { transform: translateX(-50%) rotate(-60deg); z-index: 1; }
        .fan-blade:nth-child(2) { transform: translateX(-50%) rotate(-40deg); z-index: 2; }
        .fan-blade:nth-child(3) { transform: translateX(-50%) rotate(-20deg); z-index: 3; }
        .fan-blade:nth-child(4) { transform: translateX(-50%) rotate(0deg); z-index: 4; }
        .fan-blade:nth-child(5) { transform: translateX(-50%) rotate(20deg); z-index: 5; }
        .fan-blade:nth-child(6) { transform: translateX(-50%) rotate(40deg); z-index: 6; }
        .fan-blade:nth-child(7) { transform: translateX(-50%) rotate(60deg); z-index: 7; }

        /* 扇子把手 */
        .fan-handle {
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 8px;
            height: 60px;
            background: linear-gradient(to bottom, #8B4513, #654321);
            border-radius: 4px;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0,0,0,0.5);
        }

        /* 方法2: 使用CSS clip-path创建更精确的折扇 */
        .precise-fan {
            position: relative;
            width: 300px;
            height: 200px;
            margin: 50px auto;
        }

        .precise-fan-blade {
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 50px;
            height: 160px;
            background-image: url('https://picsum.photos/200/400?random=2');
            background-size: cover;
            background-position: center;
            transform-origin: bottom center;
            clip-path: polygon(20% 0%, 80% 0%, 90% 100%, 10% 100%);
            border: 1px solid rgba(139, 69, 19, 0.6);
        }

        .precise-fan-blade:nth-child(1) { transform: translateX(-50%) rotate(-50deg); z-index: 1; }
        .precise-fan-blade:nth-child(2) { transform: translateX(-50%) rotate(-30deg); z-index: 2; }
        .precise-fan-blade:nth-child(3) { transform: translateX(-50%) rotate(-10deg); z-index: 3; }
        .precise-fan-blade:nth-child(4) { transform: translateX(-50%) rotate(10deg); z-index: 4; }
        .precise-fan-blade:nth-child(5) { transform: translateX(-50%) rotate(30deg); z-index: 5; }
        .precise-fan-blade:nth-child(6) { transform: translateX(-50%) rotate(50deg); z-index: 6; }

        /* 方法3: 简化版折扇 */
        .simple-fan {
            width: 250px;
            height: 150px;
            background-image: url('https://picsum.photos/400/300?random=3');
            background-size: cover;
            background-position: center;
            clip-path: polygon(10% 100%, 90% 100%, 80% 0%, 70% 5%, 60% 0%, 50% 5%, 40% 0%, 30% 5%, 20% 0%);
            border-radius: 0 0 20px 20px;
            position: relative;
            margin: 50px auto;
            box-shadow: 0 8px 16px rgba(0,0,0,0.3);
        }

        .simple-fan::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 6px;
            height: 40px;
            background: linear-gradient(to bottom, #8B4513, #654321);
            border-radius: 3px;
        }

        /* 悬停效果 */
        .folding-fan:hover .fan-blade {
            transform: translateX(-50%) rotate(0deg) !important;
            transition: transform 0.5s ease;
        }

        .precise-fan:hover .precise-fan-blade {
            transform: translateX(-50%) rotate(0deg) !important;
            transition: transform 0.5s ease;
        }

        .simple-fan:hover {
            transform: scale(1.1);
            transition: transform 0.3s ease;
        }

        .fan-title {
            color: white;
            font-size: 1.5em;
            margin-bottom: 20px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .description {
            color: rgba(255, 255, 255, 0.8);
            margin-top: 20px;
            font-size: 0.9em;
            max-width: 300px;
            margin-left: auto;
            margin-right: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>折扇图形展示</h1>
        
        <div class="fan-container">
            <div>
                <div class="fan-title">多扇叶折扇</div>
                <div class="folding-fan">
                    <div class="fan-blade"></div>
                    <div class="fan-blade"></div>
                    <div class="fan-blade"></div>
                    <div class="fan-blade"></div>
                    <div class="fan-blade"></div>
                    <div class="fan-blade"></div>
                    <div class="fan-blade"></div>
                    <div class="fan-handle"></div>
                </div>
                <div class="description">鼠标悬停可以看到折扇展开效果</div>
            </div>

            <div>
                <div class="fan-title">精确折扇</div>
                <div class="precise-fan">
                    <div class="precise-fan-blade"></div>
                    <div class="precise-fan-blade"></div>
                    <div class="precise-fan-blade"></div>
                    <div class="precise-fan-blade"></div>
                    <div class="precise-fan-blade"></div>
                    <div class="precise-fan-blade"></div>
                    <div class="fan-handle"></div>
                </div>
                <div class="description">使用clip-path创建更精确的扇叶形状</div>
            </div>

            <div>
                <div class="fan-title">简化折扇</div>
                <div class="simple-fan"></div>
                <div class="description">单个元素实现的简化版折扇</div>
            </div>
        </div>
    </div>
</body>
</html>
