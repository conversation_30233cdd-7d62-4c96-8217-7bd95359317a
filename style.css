/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
}

h1 {
    text-align: center;
    color: white;
    margin-bottom: 40px;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

h2 {
    color: white;
    margin-bottom: 20px;
    font-size: 1.5em;
}

.sector-container {
    margin-bottom: 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* 方法1: 使用clip-path创建扇形 */
.sector-clippath {
    width: 200px;
    height: 200px;
    background-image: url('https://picsum.photos/400/400?random=1');
    background-size: cover;
    background-position: center;
    clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 50%);
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.sector-clippath:hover {
    transform: scale(1.1) rotate(5deg);
}

/* 方法2: 使用border-radius创建四分之一圆扇形 */
.sector-border {
    width: 200px;
    height: 200px;
    background-image: url('https://picsum.photos/400/400?random=2');
    background-size: cover;
    background-position: center;
    border-radius: 0 0 200px 0;
    transition: transform 0.3s ease;
}

.sector-border:hover {
    transform: scale(1.1);
}

/* 方法3: 使用transform旋转创建扇形 */
.sector-transform-container {
    width: 200px;
    height: 200px;
    overflow: hidden;
    border-radius: 50%;
    position: relative;
}

.sector-transform {
    width: 200px;
    height: 200px;
    background-image: url('https://picsum.photos/400/400?random=3');
    background-size: cover;
    background-position: center;
    clip-path: polygon(50% 50%, 50% 0%, 85.36% 14.64%, 100% 50%);
    transition: transform 0.3s ease;
}

.sector-transform:hover {
    transform: rotate(10deg);
}

/* 方法4: 可调节角度的扇形 */
.sector-adjustable {
    width: 200px;
    height: 200px;
    background-image: url('https://picsum.photos/400/400?random=4');
    background-size: cover;
    background-position: center;
    border-radius: 50%;
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.sector-adjustable::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 50%;
    height: 100%;
    background: white;
    transform-origin: left center;
    transform: rotate(var(--angle, 90deg));
    z-index: 1;
}

.sector-adjustable::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 50%;
    height: 100%;
    background: white;
    z-index: 1;
}

.sector-adjustable:hover {
    transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 20px;
    }
    
    h1 {
        font-size: 2em;
    }
    
    .sector-clippath,
    .sector-border,
    .sector-transform-container,
    .sector-transform,
    .sector-adjustable {
        width: 150px;
        height: 150px;
    }
    
    .sector-border {
        border-radius: 0 0 150px 0;
    }
}
