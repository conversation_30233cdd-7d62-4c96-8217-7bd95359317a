<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扇形图片展示</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>扇形图片展示</h1>
        
        <!-- 方法1: 使用clip-path创建扇形 -->
        <div class="sector-container">
            <h2>方法1: clip-path扇形</h2>
            <div class="sector-clippath"></div>
        </div>
        
        <!-- 方法2: 使用border-radius创建四分之一圆扇形 -->
        <div class="sector-container">
            <h2>方法2: border-radius扇形</h2>
            <div class="sector-border"></div>
        </div>
        
        <!-- 方法3: 使用transform旋转创建扇形 -->
        <div class="sector-container">
            <h2>方法3: transform扇形</h2>
            <div class="sector-transform-container">
                <div class="sector-transform"></div>
            </div>
        </div>
        
        <!-- 方法4: 可调节角度的扇形 -->
        <div class="sector-container">
            <h2>方法4: 可调节角度扇形</h2>
            <div class="sector-adjustable" style="--angle: 120deg;"></div>
        </div>
    </div>
</body>
</html>
