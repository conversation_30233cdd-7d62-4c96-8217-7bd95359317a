<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扇形图片展示 - 本地图片版本</title>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .sector-item {
            text-align: center;
        }

        .sector-item h3 {
            color: white;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        /* 扇形样式 - 使用渐变色作为示例 */
        .sector {
            width: 200px;
            height: 200px;
            margin: 0 auto;
            transition: transform 0.3s ease;
            cursor: pointer;
        }

        /* 90度扇形 */
        .sector-90 {
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 50%);
        }

        /* 120度扇形 */
        .sector-120 {
            background: linear-gradient(45deg, #48cae4, #023e8a);
            clip-path: polygon(50% 50%, 50% 0%, 93.3% 25%, 93.3% 75%);
        }

        /* 180度扇形（半圆） */
        .sector-180 {
            background: linear-gradient(45deg, #f72585, #b5179e);
            clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 100%, 0% 100%, 0% 0%);
        }

        /* 60度扇形 */
        .sector-60 {
            background: linear-gradient(45deg, #06ffa5, #00d4aa);
            clip-path: polygon(50% 50%, 50% 0%, 86.6% 25%);
        }

        .sector:hover {
            transform: scale(1.1) rotate(5deg);
        }

        .instructions {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            color: white;
        }

        .instructions h2 {
            margin-bottom: 15px;
            color: #ffd700;
        }

        .instructions p {
            margin-bottom: 10px;
            line-height: 1.6;
        }

        .instructions code {
            background: rgba(0, 0, 0, 0.3);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>扇形 DIV 展示</h1>
        
        <div class="instructions">
            <h2>使用说明</h2>
            <p>要使用你自己的图片，请将以下CSS属性添加到扇形元素中：</p>
            <p><code>background-image: url('你的图片路径.jpg');</code></p>
            <p><code>background-size: cover;</code></p>
            <p><code>background-position: center;</code></p>
            <p>下面展示了不同角度的扇形效果，目前使用渐变色作为示例。</p>
        </div>

        <div class="demo-grid">
            <div class="sector-item">
                <h3>90° 扇形</h3>
                <div class="sector sector-90"></div>
            </div>
            
            <div class="sector-item">
                <h3>120° 扇形</h3>
                <div class="sector sector-120"></div>
            </div>
            
            <div class="sector-item">
                <h3>180° 扇形</h3>
                <div class="sector sector-180"></div>
            </div>
            
            <div class="sector-item">
                <h3>60° 扇形</h3>
                <div class="sector sector-60"></div>
            </div>
        </div>
    </div>
</body>
</html>
