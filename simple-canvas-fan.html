<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单Canvas折扇</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }
        
        h1 {
            color: white;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        canvas {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            cursor: pointer;
            margin: 20px;
            transition: transform 0.3s ease;
        }
        
        canvas:hover {
            transform: scale(1.05);
        }
        
        .info {
            color: white;
            margin-top: 20px;
            font-size: 14px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Canvas 折扇绘制</h1>
        <canvas id="fanCanvas" width="500" height="400"></canvas>
        <div class="info">点击画布重新绘制折扇</div>
    </div>

    <script>
        class SimpleFoldingFan {
            constructor(canvasId) {
                this.canvas = document.getElementById(canvasId);
                this.ctx = this.canvas.getContext('2d');
                this.centerX = this.canvas.width / 2;
                this.centerY = this.canvas.height - 80;
                
                // 折扇参数
                this.bladeCount = 9;
                this.fanRadius = 180;
                this.fanAngle = 140; // 总角度
                this.handleLength = 70;
                
                this.init();
            }
            
            init() {
                this.drawFan();
                this.addEventListeners();
            }
            
            addEventListeners() {
                this.canvas.addEventListener('click', () => {
                    this.drawFan();
                });
            }
            
            drawFan() {
                // 清空画布
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                
                // 绘制背景渐变
                this.drawBackground();
                
                // 绘制扇叶
                this.drawFanBlades();
                
                // 绘制扇子把手
                this.drawFanHandle();
                
                // 绘制装饰
                this.drawDecorations();
            }
            
            drawBackground() {
                const gradient = this.ctx.createRadialGradient(
                    this.centerX, this.centerY, 0,
                    this.centerX, this.centerY, this.fanRadius + 50
                );
                gradient.addColorStop(0, 'rgba(255, 255, 255, 0.1)');
                gradient.addColorStop(1, 'rgba(255, 255, 255, 0.02)');
                
                this.ctx.fillStyle = gradient;
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
            }
            
            drawFanBlades() {
                const angleStep = (this.fanAngle * Math.PI / 180) / (this.bladeCount - 1);
                const startAngle = -(this.fanAngle * Math.PI / 180) / 2;
                
                // 从后往前绘制，创建层次感
                for (let i = 0; i < this.bladeCount; i++) {
                    const angle = startAngle + i * angleStep;
                    this.drawBlade(angle, i);
                }
            }
            
            drawBlade(angle, index) {
                this.ctx.save();
                
                // 移动到扇子中心点
                this.ctx.translate(this.centerX, this.centerY);
                this.ctx.rotate(angle);
                
                // 创建扇叶形状
                this.ctx.beginPath();
                this.ctx.moveTo(0, 0);
                
                // 扇叶的左边缘
                this.ctx.lineTo(-18, -this.fanRadius * 0.3);
                this.ctx.lineTo(-20, -this.fanRadius * 0.7);
                this.ctx.lineTo(-15, -this.fanRadius);
                
                // 扇叶顶部弧形
                this.ctx.quadraticCurveTo(0, -this.fanRadius - 15, 15, -this.fanRadius);
                
                // 扇叶的右边缘
                this.ctx.lineTo(20, -this.fanRadius * 0.7);
                this.ctx.lineTo(18, -this.fanRadius * 0.3);
                this.ctx.lineTo(0, 0);
                
                this.ctx.closePath();
                
                // 创建扇叶渐变
                const bladeGradient = this.ctx.createLinearGradient(0, 0, 0, -this.fanRadius);
                
                // 随机颜色或者固定配色方案
                const colors = [
                    ['#ff9a9e', '#fecfef'],
                    ['#a18cd1', '#fbc2eb'],
                    ['#fad0c4', '#ffd1ff'],
                    ['#a8edea', '#fed6e3'],
                    ['#d299c2', '#fef9d7'],
                    ['#89f7fe', '#66a6ff'],
                    ['#fdbb2d', '#22c1c3'],
                    ['#ee9ca7', '#ffdde1'],
                    ['#f093fb', '#f5576c']
                ];
                
                const colorPair = colors[index % colors.length];
                bladeGradient.addColorStop(0, colorPair[0]);
                bladeGradient.addColorStop(1, colorPair[1]);
                
                this.ctx.fillStyle = bladeGradient;
                this.ctx.fill();
                
                // 扇叶边框
                this.ctx.strokeStyle = 'rgba(139, 69, 19, 0.6)';
                this.ctx.lineWidth = 1.5;
                this.ctx.stroke();
                
                // 绘制扇骨（竹条）
                this.drawFanRib();
                
                this.ctx.restore();
            }
            
            drawFanRib() {
                // 主扇骨
                this.ctx.beginPath();
                this.ctx.moveTo(0, 0);
                this.ctx.lineTo(0, -this.fanRadius);
                this.ctx.strokeStyle = 'rgba(101, 67, 33, 0.8)';
                this.ctx.lineWidth = 2;
                this.ctx.stroke();
                
                // 扇骨装饰线
                for (let i = 1; i <= 3; i++) {
                    this.ctx.beginPath();
                    this.ctx.moveTo(-1, -this.fanRadius * i / 4);
                    this.ctx.lineTo(1, -this.fanRadius * i / 4);
                    this.ctx.strokeStyle = 'rgba(101, 67, 33, 0.4)';
                    this.ctx.lineWidth = 0.5;
                    this.ctx.stroke();
                }
            }
            
            drawFanHandle() {
                // 扇子把手主体
                const handleGradient = this.ctx.createLinearGradient(
                    this.centerX - 10, this.centerY,
                    this.centerX + 10, this.centerY + this.handleLength
                );
                handleGradient.addColorStop(0, '#8B4513');
                handleGradient.addColorStop(0.3, '#A0522D');
                handleGradient.addColorStop(0.7, '#654321');
                handleGradient.addColorStop(1, '#4A2C17');
                
                this.ctx.fillStyle = handleGradient;
                this.ctx.fillRect(
                    this.centerX - 10, 
                    this.centerY, 
                    20, 
                    this.handleLength
                );
                
                // 把手边框
                this.ctx.strokeStyle = '#2F1B14';
                this.ctx.lineWidth = 1;
                this.ctx.strokeRect(
                    this.centerX - 10, 
                    this.centerY, 
                    20, 
                    this.handleLength
                );
            }
            
            drawDecorations() {
                // 把手上的装饰环
                const decorations = [
                    { y: this.centerY + 15, size: 5 },
                    { y: this.centerY + 35, size: 4 },
                    { y: this.centerY + 55, size: 5 }
                ];
                
                decorations.forEach(decoration => {
                    // 金色装饰环
                    this.ctx.fillStyle = '#DAA520';
                    this.ctx.beginPath();
                    this.ctx.arc(this.centerX, decoration.y, decoration.size, 0, Math.PI * 2);
                    this.ctx.fill();
                    
                    // 装饰环边框
                    this.ctx.strokeStyle = '#B8860B';
                    this.ctx.lineWidth = 1;
                    this.ctx.stroke();
                });
                
                // 扇子中心装饰
                this.ctx.fillStyle = '#8B4513';
                this.ctx.beginPath();
                this.ctx.arc(this.centerX, this.centerY, 8, 0, Math.PI * 2);
                this.ctx.fill();
                
                this.ctx.strokeStyle = '#654321';
                this.ctx.lineWidth = 2;
                this.ctx.stroke();
            }
        }
        
        // 初始化折扇
        window.addEventListener('DOMContentLoaded', () => {
            new SimpleFoldingFan('fanCanvas');
        });
    </script>
</body>
</html>
