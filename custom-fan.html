<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自定义折扇</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .instructions {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            color: white;
        }

        .instructions h2 {
            margin-bottom: 15px;
            color: #ffd700;
        }

        .instructions p {
            margin-bottom: 10px;
            line-height: 1.6;
        }

        .instructions code {
            background: rgba(0, 0, 0, 0.3);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }

        /* 主要的折扇样式 */
        .main-fan {
            position: relative;
            width: 350px;
            height: 220px;
            margin: 40px auto;
        }

        .fan-segment {
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 45px;
            height: 180px;
            transform-origin: bottom center;
            border-radius: 22px 22px 0 0;
            border: 2px solid rgba(139, 69, 19, 0.8);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        /* 使用渐变色作为默认背景，你可以替换为图片 */
        .fan-segment:nth-child(1) { 
            background: linear-gradient(to bottom, #ff9a9e, #fecfef);
            transform: translateX(-50%) rotate(-70deg); 
            z-index: 1; 
        }
        .fan-segment:nth-child(2) { 
            background: linear-gradient(to bottom, #a18cd1, #fbc2eb);
            transform: translateX(-50%) rotate(-50deg); 
            z-index: 2; 
        }
        .fan-segment:nth-child(3) { 
            background: linear-gradient(to bottom, #fad0c4, #ffd1ff);
            transform: translateX(-50%) rotate(-30deg); 
            z-index: 3; 
        }
        .fan-segment:nth-child(4) { 
            background: linear-gradient(to bottom, #a8edea, #fed6e3);
            transform: translateX(-50%) rotate(-10deg); 
            z-index: 4; 
        }
        .fan-segment:nth-child(5) { 
            background: linear-gradient(to bottom, #d299c2, #fef9d7);
            transform: translateX(-50%) rotate(10deg); 
            z-index: 5; 
        }
        .fan-segment:nth-child(6) { 
            background: linear-gradient(to bottom, #89f7fe, #66a6ff);
            transform: translateX(-50%) rotate(30deg); 
            z-index: 6; 
        }
        .fan-segment:nth-child(7) { 
            background: linear-gradient(to bottom, #fdbb2d, #22c1c3);
            transform: translateX(-50%) rotate(50deg); 
            z-index: 7; 
        }
        .fan-segment:nth-child(8) { 
            background: linear-gradient(to bottom, #ee9ca7, #ffdde1);
            transform: translateX(-50%) rotate(70deg); 
            z-index: 8; 
        }

        /* 扇子把手 */
        .fan-handle {
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 12px;
            height: 80px;
            background: linear-gradient(to bottom, #8B4513, #654321, #4A2C17);
            border-radius: 6px;
            z-index: 15;
            box-shadow: 0 4px 8px rgba(0,0,0,0.5);
        }

        .fan-handle::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            width: 8px;
            height: 8px;
            background: #DAA520;
            border-radius: 50%;
        }

        .fan-handle::after {
            content: '';
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            width: 8px;
            height: 8px;
            background: #DAA520;
            border-radius: 50%;
        }

        /* 动画效果 */
        .main-fan:hover .fan-segment {
            animation: fanOpen 0.8s ease-in-out;
        }

        @keyframes fanOpen {
            0% { transform: translateX(-50%) rotate(var(--start-angle)); }
            50% { transform: translateX(-50%) rotate(0deg) scale(1.05); }
            100% { transform: translateX(-50%) rotate(var(--start-angle)); }
        }

        /* 使用图片的示例类 */
        .with-image {
            background-image: url('your-image.jpg') !important;
            background-size: cover !important;
            background-position: center !important;
            background-repeat: no-repeat !important;
        }

        .demo-section {
            text-align: center;
            margin: 40px 0;
        }

        .demo-title {
            color: white;
            font-size: 1.8em;
            margin-bottom: 20px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>自定义折扇图形</h1>
        
        <div class="instructions">
            <h2>如何使用你的图片</h2>
            <p>要将你的图片应用到折扇上，请按以下步骤操作：</p>
            <p>1. 将你的图片文件放在与HTML文件相同的文件夹中</p>
            <p>2. 修改CSS中的背景图片路径：</p>
            <p><code>background-image: url('你的图片名称.jpg');</code></p>
            <p>3. 或者为扇叶添加 <code>with-image</code> 类，并修改对应的CSS</p>
            <p>4. 你可以为每个扇叶使用不同的图片，创造更丰富的视觉效果</p>
        </div>

        <div class="demo-section">
            <div class="demo-title">渐变色折扇（鼠标悬停查看动画）</div>
            <div class="main-fan">
                <div class="fan-segment"></div>
                <div class="fan-segment"></div>
                <div class="fan-segment"></div>
                <div class="fan-segment"></div>
                <div class="fan-segment"></div>
                <div class="fan-segment"></div>
                <div class="fan-segment"></div>
                <div class="fan-segment"></div>
                <div class="fan-handle"></div>
            </div>
        </div>
    </div>

    <script>
        // 为动画设置CSS变量
        document.querySelectorAll('.fan-segment').forEach((segment, index) => {
            const angles = [-70, -50, -30, -10, 10, 30, 50, 70];
            segment.style.setProperty('--start-angle', angles[index] + 'deg');
        });
    </script>
</body>
</html>
