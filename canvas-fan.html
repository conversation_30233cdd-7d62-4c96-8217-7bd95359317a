<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas 折扇绘制</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .canvas-container {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            justify-content: center;
            margin: 30px 0;
        }

        .canvas-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.2);
        }

        .canvas-item h3 {
            color: white;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        canvas {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.05);
        }

        .controls {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            color: white;
        }

        .control-group {
            margin: 15px 0;
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .control-group label {
            min-width: 120px;
            font-weight: bold;
        }

        .control-group input, .control-group select {
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.9);
            font-size: 14px;
        }

        .control-group button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .control-group button:hover {
            transform: translateY(-2px);
        }

        .file-input {
            display: none;
        }

        .file-label {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            background: linear-gradient(45deg, #48cae4, #023e8a);
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s ease;
            display: inline-block;
        }

        .file-label:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Canvas 折扇绘制</h1>
        
        <div class="controls">
            <h2 style="color: #ffd700; margin-bottom: 20px;">控制面板</h2>
            
            <div class="control-group">
                <label>扇叶数量:</label>
                <input type="range" id="bladeCount" min="5" max="15" value="8">
                <span id="bladeCountValue">8</span>
            </div>
            
            <div class="control-group">
                <label>扇子角度:</label>
                <input type="range" id="fanAngle" min="60" max="180" value="120">
                <span id="fanAngleValue">120°</span>
            </div>
            
            <div class="control-group">
                <label>扇子大小:</label>
                <input type="range" id="fanSize" min="100" max="300" value="200">
                <span id="fanSizeValue">200</span>
            </div>
            
            <div class="control-group">
                <label>填充模式:</label>
                <select id="fillMode">
                    <option value="gradient">渐变色</option>
                    <option value="pattern">图案</option>
                    <option value="image">图片</option>
                </select>
            </div>
            
            <div class="control-group">
                <label>选择图片:</label>
                <input type="file" id="imageInput" class="file-input" accept="image/*">
                <label for="imageInput" class="file-label">选择图片</label>
            </div>
            
            <div class="control-group">
                <button onclick="animateFan()">动画展示</button>
                <button onclick="downloadCanvas()">下载图片</button>
            </div>
        </div>

        <div class="canvas-container">
            <div class="canvas-item">
                <h3>交互式折扇</h3>
                <canvas id="interactiveFan" width="400" height="300"></canvas>
            </div>
            
            <div class="canvas-item">
                <h3>动画折扇</h3>
                <canvas id="animatedFan" width="400" height="300"></canvas>
            </div>
        </div>
    </div>

    <script>
        class FoldingFan {
            constructor(canvas, options = {}) {
                this.canvas = canvas;
                this.ctx = canvas.getContext('2d');
                this.centerX = canvas.width / 2;
                this.centerY = canvas.height - 50;
                
                // 默认参数
                this.bladeCount = options.bladeCount || 8;
                this.fanAngle = options.fanAngle || 120;
                this.fanRadius = options.fanRadius || 200;
                this.handleLength = options.handleLength || 60;
                this.image = null;
                this.animationFrame = 0;
                
                this.init();
            }
            
            init() {
                this.draw();
            }
            
            setImage(image) {
                this.image = image;
                this.draw();
            }
            
            updateParams(bladeCount, fanAngle, fanRadius) {
                this.bladeCount = bladeCount;
                this.fanAngle = fanAngle;
                this.fanRadius = fanRadius;
                this.draw();
            }
            
            draw() {
                // 清空画布
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                
                // 绘制背景
                this.drawBackground();
                
                // 绘制扇叶
                this.drawBlades();
                
                // 绘制扇子把手
                this.drawHandle();
            }
            
            drawBackground() {
                const gradient = this.ctx.createRadialGradient(
                    this.centerX, this.centerY, 0,
                    this.centerX, this.centerY, this.fanRadius
                );
                gradient.addColorStop(0, 'rgba(255, 255, 255, 0.1)');
                gradient.addColorStop(1, 'rgba(255, 255, 255, 0.05)');
                
                this.ctx.fillStyle = gradient;
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
            }
            
            drawBlades() {
                const angleStep = (this.fanAngle * Math.PI / 180) / (this.bladeCount - 1);
                const startAngle = -this.fanAngle * Math.PI / 360;
                
                for (let i = 0; i < this.bladeCount; i++) {
                    const angle = startAngle + i * angleStep;
                    this.drawSingleBlade(angle, i);
                }
            }
            
            drawSingleBlade(angle, index) {
                this.ctx.save();
                
                // 移动到扇子中心
                this.ctx.translate(this.centerX, this.centerY);
                this.ctx.rotate(angle);
                
                // 创建扇叶路径
                this.ctx.beginPath();
                this.ctx.moveTo(0, 0);
                this.ctx.lineTo(-15, -this.fanRadius);
                this.ctx.quadraticCurveTo(0, -this.fanRadius - 10, 15, -this.fanRadius);
                this.ctx.lineTo(0, 0);
                this.ctx.closePath();
                
                // 填充扇叶
                if (this.image) {
                    // 使用图片填充
                    const pattern = this.ctx.createPattern(this.image, 'repeat');
                    this.ctx.fillStyle = pattern;
                } else {
                    // 使用渐变色填充
                    const gradient = this.ctx.createLinearGradient(0, 0, 0, -this.fanRadius);
                    const hue = (index * 360 / this.bladeCount + this.animationFrame) % 360;
                    gradient.addColorStop(0, `hsla(${hue}, 70%, 60%, 0.8)`);
                    gradient.addColorStop(1, `hsla(${hue + 30}, 70%, 80%, 0.9)`);
                    this.ctx.fillStyle = gradient;
                }
                
                this.ctx.fill();
                
                // 绘制扇叶边框
                this.ctx.strokeStyle = 'rgba(139, 69, 19, 0.8)';
                this.ctx.lineWidth = 2;
                this.ctx.stroke();
                
                // 绘制扇骨
                this.ctx.beginPath();
                this.ctx.moveTo(0, 0);
                this.ctx.lineTo(0, -this.fanRadius);
                this.ctx.strokeStyle = 'rgba(139, 69, 19, 0.6)';
                this.ctx.lineWidth = 1;
                this.ctx.stroke();
                
                this.ctx.restore();
            }
            
            drawHandle() {
                // 绘制扇子把手
                const gradient = this.ctx.createLinearGradient(
                    this.centerX - 8, this.centerY,
                    this.centerX + 8, this.centerY + this.handleLength
                );
                gradient.addColorStop(0, '#8B4513');
                gradient.addColorStop(0.5, '#654321');
                gradient.addColorStop(1, '#4A2C17');
                
                this.ctx.fillStyle = gradient;
                this.ctx.fillRect(
                    this.centerX - 8, 
                    this.centerY, 
                    16, 
                    this.handleLength
                );
                
                // 把手装饰
                this.ctx.fillStyle = '#DAA520';
                this.ctx.beginPath();
                this.ctx.arc(this.centerX, this.centerY + 15, 4, 0, Math.PI * 2);
                this.ctx.fill();
                
                this.ctx.beginPath();
                this.ctx.arc(this.centerX, this.centerY + this.handleLength - 15, 4, 0, Math.PI * 2);
                this.ctx.fill();
            }
            
            animate() {
                this.animationFrame += 2;
                this.draw();
                requestAnimationFrame(() => this.animate());
            }
        }
        
        // 初始化折扇
        const interactiveFan = new FoldingFan(document.getElementById('interactiveFan'));
        const animatedFan = new FoldingFan(document.getElementById('animatedFan'));
        
        // 启动动画折扇
        animatedFan.animate();
        
        // 控制面板事件
        document.getElementById('bladeCount').addEventListener('input', updateFan);
        document.getElementById('fanAngle').addEventListener('input', updateFan);
        document.getElementById('fanSize').addEventListener('input', updateFan);
        
        function updateFan() {
            const bladeCount = parseInt(document.getElementById('bladeCount').value);
            const fanAngle = parseInt(document.getElementById('fanAngle').value);
            const fanSize = parseInt(document.getElementById('fanSize').value);
            
            document.getElementById('bladeCountValue').textContent = bladeCount;
            document.getElementById('fanAngleValue').textContent = fanAngle + '°';
            document.getElementById('fanSizeValue').textContent = fanSize;
            
            interactiveFan.updateParams(bladeCount, fanAngle, fanSize);
        }
        
        // 图片上传
        document.getElementById('imageInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = new Image();
                    img.onload = function() {
                        interactiveFan.setImage(img);
                        animatedFan.setImage(img);
                    };
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
        
        // 动画展示
        function animateFan() {
            let angle = 0;
            const animate = () => {
                angle += 2;
                interactiveFan.animationFrame = angle;
                interactiveFan.draw();
                if (angle < 360) {
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }
        
        // 下载画布
        function downloadCanvas() {
            const link = document.createElement('a');
            link.download = 'folding-fan.png';
            link.href = document.getElementById('interactiveFan').toDataURL();
            link.click();
        }
    </script>
</body>
</html>
